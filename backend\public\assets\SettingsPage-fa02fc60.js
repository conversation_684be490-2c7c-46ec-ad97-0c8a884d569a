import{r as l,j as e,D as $,S as le,B as x,C as V,n as Z,L as ce,K as G,p as L,A as H,T as se,E as R,i as W,U as Q,V as Y,H as B,d as U,W as K,I as O,X,_ as J,b as oe,Y as de,Z as me,$ as ue,a0 as xe,a1 as he,y as pe,x as fe}from"./index-8171bd56.js";import{D as ge,a as ye}from"./DifficultySelector-a29b1156.js";const ee=[{id:"free",name:"Study Starter",price:0,interval:"month",features:["500 AI study generations per month","Basic flashcards and quizzes","Up to 5 document uploads","Basic study analytics","Perfect for trying out ChewyAI"]},{id:"pro_monthly",name:"Scholar Pro",price:9.99,interval:"month",popular:!0,features:["Unlimited AI study generations","Advanced study modes & spaced repetition","Unlimited document uploads","Detailed progress analytics","Priority support during finals","Export study materials","Perfect for serious students"]},{id:"pro_yearly",name:"Academic Year Pass",price:99.99,interval:"year",features:["Unlimited AI study generations","Advanced study modes & spaced repetition","Unlimited document uploads","Detailed progress analytics","Priority support during finals","Export study materials","Save $20 vs monthly (2 months free!)","Perfect for the full academic year"]}],be=()=>{var u,N;const[a,k]=l.useState(null),[w,m]=l.useState(!0),[y,c]=l.useState(!1),[b,n]=l.useState(null);l.useEffect(()=>{d()},[]);const d=async()=>{m(!0),n(null);try{const s=localStorage.getItem("auth_token"),t=await fetch("/api/subscription",{headers:{Authorization:`Bearer ${s}`}});if(!t.ok)throw new Error("Failed to fetch subscription data");const r=await t.json();if(r.success)k(r.data);else throw new Error(r.error)}catch(s){n(s instanceof Error?s.message:"Failed to load subscription data"),k({currentPlan:ee[0],status:"active",nextBillingDate:new Date(Date.now()+30*24*60*60*1e3).toISOString()})}finally{m(!1)}},f=async s=>{c(!0),n(null);try{const t=localStorage.getItem("auth_token"),r=await fetch("/api/subscription/change",{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({planId:s})});if(!r.ok)throw new Error("Failed to change subscription plan");const p=await r.json();if(p.success)await d();else throw new Error(p.error)}catch(t){n(t instanceof Error?t.message:"Failed to change subscription plan")}finally{c(!1)}},h=async()=>{if(confirm("Are you sure you want to cancel your subscription? You will lose access to Pro features at the end of your billing period.")){c(!0),n(null);try{const s=localStorage.getItem("auth_token"),t=await fetch("/api/subscription/cancel",{method:"POST",headers:{Authorization:`Bearer ${s}`}});if(!t.ok)throw new Error("Failed to cancel subscription");const r=await t.json();if(r.success)await d();else throw new Error(r.error)}catch(s){n(s instanceof Error?s.message:"Failed to cancel subscription")}finally{c(!1)}}},o=async()=>{c(!0),n(null);try{const s=localStorage.getItem("auth_token"),t=await fetch("/api/subscription/reactivate",{method:"POST",headers:{Authorization:`Bearer ${s}`}});if(!t.ok)throw new Error("Failed to reactivate subscription");const r=await t.json();if(r.success)await d();else throw new Error(r.error)}catch(s){n(s instanceof Error?s.message:"Failed to reactivate subscription")}finally{c(!1)}};return w?e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"animate-pulse",children:[e.jsx("div",{className:"h-8 w-48 bg-gray-600 rounded mb-4"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[1,2,3].map(s=>e.jsx("div",{className:"h-64 bg-gray-600 rounded-lg"},s))})]})}):e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Subscription Management"}),b&&e.jsxs("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx($,{className:"w-5 h-5 text-red-400"}),e.jsx("span",{className:"text-red-400 font-medium",children:"Error"})]}),e.jsx("p",{className:"text-red-300 mt-1",children:b})]}),a&&e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-medium text-white",children:"Current Plan"}),e.jsx("p",{className:"text-gray-400",children:((u=a.currentPlan)==null?void 0:u.name)||"No active plan"})]}),e.jsx("div",{className:"text-right",children:e.jsx("div",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${a.status==="active"?"bg-green-500/20 text-green-400":a.status==="canceled"?"bg-red-500/20 text-red-400":a.status==="past_due"?"bg-yellow-500/20 text-yellow-400":"bg-blue-500/20 text-blue-400"}`,children:a.status.charAt(0).toUpperCase()+a.status.slice(1)})})]}),a.nextBillingDate&&e.jsxs("div",{className:"flex items-center space-x-2 text-gray-400 text-sm",children:[e.jsx(le,{className:"w-4 h-4"}),e.jsx("span",{children:a.cancelAtPeriodEnd?`Access ends on ${new Date(a.nextBillingDate).toLocaleDateString()}`:`Next billing date: ${new Date(a.nextBillingDate).toLocaleDateString()}`})]}),((N=a.currentPlan)==null?void 0:N.id)!=="free"&&e.jsxs("div",{className:"mt-4 flex space-x-3",children:[a.cancelAtPeriodEnd?e.jsx(x,{onClick:o,isLoading:y,variant:"primary",size:"sm",children:"Reactivate Subscription"}):e.jsx(x,{onClick:h,isLoading:y,variant:"danger",size:"sm",children:"Cancel Subscription"}),e.jsxs(x,{onClick:d,variant:"secondary",size:"sm",children:[e.jsx(V,{className:"w-4 h-4 mr-2"}),"Refresh"]})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-medium text-white mb-4",children:"Available Plans"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:ee.map(s=>{var r;const t=((r=a==null?void 0:a.currentPlan)==null?void 0:r.id)===s.id;return e.jsxs(Z.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:`relative bg-background-secondary rounded-lg p-6 border transition-all duration-200 ${s.popular?"border-primary-500 ring-2 ring-primary-500/20":t?"border-green-500 ring-2 ring-green-500/20":"border-border-primary hover:border-gray-500"}`,children:[s.popular&&e.jsx("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2",children:e.jsxs("div",{className:"bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1",children:[e.jsx(ce,{className:"w-3 h-3"}),e.jsx("span",{children:"Most Popular"})]})}),t&&e.jsx("div",{className:"absolute -top-3 right-4",children:e.jsxs("div",{className:"bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1",children:[e.jsx(G,{className:"w-3 h-3"}),e.jsx("span",{children:"Current"})]})}),e.jsxs("div",{className:"text-center mb-6",children:[e.jsx("h5",{className:"text-xl font-semibold text-white mb-2",children:s.name}),e.jsxs("div",{className:"text-3xl font-bold text-white",children:["$",s.price,e.jsxs("span",{className:"text-lg text-gray-400",children:["/",s.interval]})]})]}),e.jsx("ul",{className:"space-y-3 mb-6",children:s.features.map((p,v)=>e.jsxs("li",{className:"flex items-center space-x-2",children:[e.jsx(G,{className:"w-4 h-4 text-green-400 flex-shrink-0"}),e.jsx("span",{className:"text-gray-300 text-sm",children:p})]},v))}),e.jsx(x,{onClick:()=>f(s.id),disabled:t||y,isLoading:y,variant:s.popular?"primary":"secondary",className:"w-full",children:t?"Current Plan":`Switch to ${s.name}`})]},s.id)})})]})]})})},je=()=>{const[a,k]=l.useState(!1),[w,m]=l.useState(!1),[y,c]=l.useState(null),[b,n]=l.useState(null),[d,f]=l.useState({studySets:12,flashcards:245,quizzes:18,documents:8,totalSize:"2.4 MB"}),[h,o]=l.useState({studySets:!0,flashcards:!0,quizzes:!0,analytics:!0,preferences:!0}),u=async()=>{k(!0),c(null),n(null);try{const t=localStorage.getItem("auth_token"),r=await fetch("/api/data/export",{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({exportData:h})});if(!r.ok)throw new Error("Failed to export data");const p=await r.blob(),v=window.URL.createObjectURL(p),C=document.createElement("a");C.href=v,C.download=`chewyai-data-export-${new Date().toISOString().split("T")[0]}.json`,C.click(),window.URL.revokeObjectURL(v),n("Data exported successfully!")}catch(t){c(t instanceof Error?t.message:"Failed to export data")}finally{k(!1)}},N=async t=>{if(confirm(`Are you sure you want to clear all ${t}? This action cannot be undone.`)){m(!0),c(null),n(null);try{const r=localStorage.getItem("auth_token"),p=await fetch(`/api/data/clear/${t}`,{method:"DELETE",headers:{Authorization:`Bearer ${r}`}});if(!p.ok)throw new Error(`Failed to clear ${t}`);const v=await p.json();if(v.success)n(`${t} cleared successfully!`),await s();else throw new Error(v.error)}catch(r){c(r instanceof Error?r.message:`Failed to clear ${t}`)}finally{m(!1)}}},s=async()=>{try{const t=localStorage.getItem("auth_token"),r=await fetch("/api/data/stats",{headers:{Authorization:`Bearer ${t}`}});if(r.ok){const p=await r.json();p.success&&f(p.data)}}catch{}};return e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Data Management"}),y&&e.jsxs("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx($,{className:"w-5 h-5 text-red-400"}),e.jsx("span",{className:"text-red-400 font-medium",children:"Error"})]}),e.jsx("p",{className:"text-red-300 mt-1",children:y})]}),b&&e.jsxs("div",{className:"mb-6 bg-green-500/20 border border-green-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(L,{className:"w-5 h-5 text-green-400"}),e.jsx("span",{className:"text-green-400 font-medium",children:"Success"})]}),e.jsx("p",{className:"text-green-300 mt-1",children:b})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h4",{className:"text-lg font-medium text-white",children:"Your Data Overview"}),e.jsxs(x,{onClick:s,variant:"secondary",size:"sm",children:[e.jsx(V,{className:"w-4 h-4 mr-2"}),"Refresh"]})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-400",children:d.studySets}),e.jsx("div",{className:"text-sm text-gray-400",children:"Study Sets"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-400",children:d.flashcards}),e.jsx("div",{className:"text-sm text-gray-400",children:"Flashcards"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-400",children:d.quizzes}),e.jsx("div",{className:"text-sm text-gray-400",children:"Quizzes"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-400",children:d.documents}),e.jsx("div",{className:"text-sm text-gray-400",children:"Documents"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-400",children:d.totalSize}),e.jsx("div",{className:"text-sm text-gray-400",children:"Total Size"})]})]})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[e.jsx(H,{className:"w-6 h-6 text-blue-400"}),e.jsx("h4",{className:"text-lg font-medium text-white",children:"Export Your Data"})]}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:"Download a copy of your data in JSON format. You can use this to backup your data or import it into another account."}),e.jsxs("div",{className:"space-y-3 mb-6",children:[e.jsx("h5",{className:"font-medium text-white",children:"Select data to export:"}),Object.entries(h).map(([t,r])=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm text-gray-300 capitalize",children:t.replace(/([A-Z])/g," $1").trim()}),e.jsx("button",{onClick:()=>o({...h,[t]:!r}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${r?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${r?"translate-x-6":"translate-x-1"}`})})]},t))]}),e.jsxs(x,{onClick:u,isLoading:a,variant:"primary",children:[e.jsx(H,{className:"w-4 h-4 mr-2"}),"Export Data"]})]}),e.jsxs("div",{className:"bg-red-500/10 rounded-lg p-6 border border-red-500/30",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[e.jsx(se,{className:"w-6 h-6 text-red-400"}),e.jsx("h4",{className:"text-lg font-medium text-white",children:"Clear Data"})]}),e.jsx("p",{className:"text-gray-400 text-sm mb-6",children:"Permanently delete specific types of data from your account. This action cannot be undone."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx(x,{onClick:()=>N("study-sets"),isLoading:w,variant:"danger",size:"sm",className:"w-full",children:"Clear All Study Sets"}),e.jsx(x,{onClick:()=>N("flashcards"),isLoading:w,variant:"danger",size:"sm",className:"w-full",children:"Clear All Flashcards"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(x,{onClick:()=>N("quizzes"),isLoading:w,variant:"danger",size:"sm",className:"w-full",children:"Clear All Quizzes"}),e.jsx(x,{onClick:()=>N("analytics"),isLoading:w,variant:"danger",size:"sm",className:"w-full",children:"Clear Analytics Data"})]})]})]})]})})},we=()=>{const[a,k]=l.useState(null),[w,m]=l.useState(!0),[y,c]=l.useState(!1),[b,n]=l.useState(null);l.useEffect(()=>{d()},[]);const d=async()=>{m(!0),n(null);try{const s=localStorage.getItem("auth_token"),t=await fetch("/api/billing",{headers:{Authorization:`Bearer ${s}`}});if(!t.ok)throw new Error("Failed to fetch billing data");const r=await t.json();if(r.success)k(r.data);else throw new Error(r.error)}catch(s){n(s instanceof Error?s.message:"Failed to load billing data"),k({paymentMethods:[{id:"1",type:"card",last4:"4242",brand:"visa",expiryMonth:12,expiryYear:2025,isDefault:!0}],invoices:[{id:"1",number:"INV-001",amount:9.99,currency:"USD",status:"paid",date:new Date(Date.now()-30*24*60*60*1e3).toISOString(),description:"Pro Plan - Monthly"},{id:"2",number:"INV-002",amount:9.99,currency:"USD",status:"pending",date:new Date().toISOString(),dueDate:new Date(Date.now()+7*24*60*60*1e3).toISOString(),description:"Pro Plan - Monthly"}],nextInvoice:{amount:9.99,currency:"USD",date:new Date(Date.now()+30*24*60*60*1e3).toISOString()}})}finally{m(!1)}},f=async s=>{try{const t=localStorage.getItem("auth_token"),r=await fetch(`/api/billing/invoices/${s}/download`,{headers:{Authorization:`Bearer ${t}`}});if(!r.ok)throw new Error("Failed to download invoice");const p=await r.blob(),v=window.URL.createObjectURL(p),C=document.createElement("a");C.href=v,C.download=`invoice-${s}.pdf`,C.click(),window.URL.revokeObjectURL(v)}catch(t){n(t instanceof Error?t.message:"Failed to download invoice")}},h=async()=>{n("Payment method management coming soon")},o=async s=>{c(!0),n(null);try{const t=localStorage.getItem("auth_token"),r=await fetch("/api/billing/payment-methods/default",{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({paymentMethodId:s})});if(!r.ok)throw new Error("Failed to update default payment method");const p=await r.json();if(p.success)await d();else throw new Error(p.error)}catch(t){n(t instanceof Error?t.message:"Failed to update payment method")}finally{c(!1)}},u=s=>{switch(s){case"paid":return e.jsx(L,{className:"w-5 h-5 text-green-400"});case"pending":return e.jsx(W,{className:"w-5 h-5 text-yellow-400"});case"failed":return e.jsx(Q,{className:"w-5 h-5 text-red-400"});default:return e.jsx(W,{className:"w-5 h-5 text-gray-400"})}},N=s=>{switch(s){case"paid":return"text-green-400";case"pending":return"text-yellow-400";case"failed":return"text-red-400";default:return"text-gray-400"}};return w?e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"animate-pulse",children:[e.jsx("div",{className:"h-8 w-48 bg-gray-600 rounded mb-4"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"h-32 bg-gray-600 rounded-lg"}),e.jsx("div",{className:"h-64 bg-gray-600 rounded-lg"})]})]})}):e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Billing & Invoices"}),e.jsxs(x,{onClick:d,variant:"secondary",size:"sm",children:[e.jsx(V,{className:"w-4 h-4 mr-2"}),"Refresh"]})]}),b&&e.jsxs("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx($,{className:"w-5 h-5 text-red-400"}),e.jsx("span",{className:"text-red-400 font-medium",children:"Error"})]}),e.jsx("p",{className:"text-red-300 mt-1",children:b})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h4",{className:"text-lg font-medium text-white",children:"Payment Methods"}),e.jsx(x,{onClick:h,variant:"secondary",size:"sm",children:"Add Payment Method"})]}),(a==null?void 0:a.paymentMethods.length)===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(R,{className:"w-16 h-16 text-gray-500 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-400",children:"No payment methods added"})]}):e.jsx("div",{className:"space-y-3",children:a==null?void 0:a.paymentMethods.map(s=>{var t;return e.jsxs("div",{className:"flex items-center justify-between p-4 bg-background-secondary rounded-lg border border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(R,{className:"w-6 h-6 text-gray-400"}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("span",{className:"text-white font-medium",children:[(t=s.brand)==null?void 0:t.toUpperCase()," •••• ",s.last4]}),s.isDefault&&e.jsx("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded text-xs font-medium",children:"Default"})]}),s.expiryMonth&&s.expiryYear&&e.jsxs("p",{className:"text-gray-400 text-sm",children:["Expires ",s.expiryMonth.toString().padStart(2,"0"),"/",s.expiryYear]})]})]}),!s.isDefault&&e.jsx(x,{onClick:()=>o(s.id),isLoading:y,variant:"secondary",size:"sm",children:"Set as Default"})]},s.id)})})]}),(a==null?void 0:a.nextInvoice)&&e.jsxs("div",{className:"bg-blue-500/10 rounded-lg p-6 border border-blue-500/30 mb-6",children:[e.jsx("h4",{className:"text-lg font-medium text-white mb-2",children:"Upcoming Invoice"}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("p",{className:"text-blue-300",children:["$",a.nextInvoice.amount," ",a.nextInvoice.currency.toUpperCase()]}),e.jsxs("p",{className:"text-blue-400 text-sm",children:["Due on ",new Date(a.nextInvoice.date).toLocaleDateString()]})]})})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary",children:[e.jsx("h4",{className:"text-lg font-medium text-white mb-4",children:"Invoice History"}),(a==null?void 0:a.invoices.length)===0?e.jsx("div",{className:"text-center py-8",children:e.jsx("p",{className:"text-gray-400",children:"No invoices found"})}):e.jsx("div",{className:"space-y-3",children:a==null?void 0:a.invoices.map(s=>e.jsxs(Z.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"flex items-center justify-between p-4 bg-background-secondary rounded-lg border border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[u(s.status),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-white font-medium",children:s.number}),e.jsx("span",{className:`text-sm font-medium ${N(s.status)}`,children:s.status.charAt(0).toUpperCase()+s.status.slice(1)})]}),e.jsx("p",{className:"text-gray-400 text-sm",children:s.description}),e.jsxs("p",{className:"text-gray-500 text-xs",children:[new Date(s.date).toLocaleDateString(),s.dueDate&&s.status==="pending"&&e.jsxs("span",{children:[" • Due ",new Date(s.dueDate).toLocaleDateString()]})]})]})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("span",{className:"text-white font-medium",children:["$",s.amount," ",s.currency.toUpperCase()]}),s.status==="paid"&&e.jsxs(x,{onClick:()=>f(s.id),variant:"secondary",size:"sm",children:[e.jsx(H,{className:"w-4 h-4 mr-2"}),"Download"]})]})]},s.id))})]})]})})},Ne=({enabled:a,onToggle:k})=>{const[w,m]=l.useState(!1),[y,c]=l.useState(!1),[b,n]=l.useState(null),[d,f]=l.useState(null),[h,o]=l.useState(null),[u,N]=l.useState(""),[s,t]=l.useState(!1),[r,p]=l.useState(!1),v=async()=>{c(!0),n(null),f(null);try{const{createClient:j}=await J(()=>import("./index-8171bd56.js").then(z=>z.a2),["assets/index-8171bd56.js","assets/index-72f36d3a.css"]),I=j("https://jpvbtrzvbpyzgtpvltss.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.TTEAu4XUOXRW-gBvs1qSlSx92fnW7apyMY_KTnQiUbI"),{data:M,error:P}=await I.auth.mfa.enroll({factorType:"totp",friendlyName:"ChewyAI Authenticator"});if(P)throw new Error(P.message);o({qrCode:M.totp.qr_code,secret:M.totp.secret,backupCodes:[],factorId:M.id}),m(!0)}catch(j){n(j instanceof Error?j.message:"Failed to setup 2FA"),o({qrCode:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",secret:"JBSWY3DPEHPK3PXP",factorId:"mock-factor-id",backupCodes:["12345678","87654321","11111111","22222222","33333333","44444444","55555555","66666666"]}),m(!0)}finally{c(!1)}},C=async()=>{if(!u||u.length!==6){n("Please enter a valid 6-digit code");return}c(!0),n(null);try{const{createClient:j}=await J(()=>import("./index-8171bd56.js").then(_=>_.a2),["assets/index-8171bd56.js","assets/index-72f36d3a.css"]),I=j("https://jpvbtrzvbpyzgtpvltss.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.TTEAu4XUOXRW-gBvs1qSlSx92fnW7apyMY_KTnQiUbI");if(!h)throw new Error("No setup data available");const{data:M,error:P}=await I.auth.mfa.challenge({factorId:h.factorId});if(P)throw new Error(P.message);const{error:z}=await I.auth.mfa.verify({factorId:h.factorId,challengeId:M.id,code:u});if(z)throw new Error(z.message);f("Two-factor authentication enabled successfully!"),m(!1),p(!0),k(!0)}catch(j){n(j instanceof Error?j.message:"Failed to verify 2FA code")}finally{c(!1)}},A=async()=>{if(confirm("Are you sure you want to disable two-factor authentication? This will make your account less secure.")){c(!0),n(null);try{const{createClient:j}=await J(()=>import("./index-8171bd56.js").then(z=>z.a2),["assets/index-8171bd56.js","assets/index-72f36d3a.css"]),I=j("https://jpvbtrzvbpyzgtpvltss.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.TTEAu4XUOXRW-gBvs1qSlSx92fnW7apyMY_KTnQiUbI"),{data:M,error:P}=await I.auth.mfa.listFactors();if(P)throw new Error(P.message);for(const z of M.totp){const{error:_}=await I.auth.mfa.unenroll({factorId:z.id});_&&console.error("Failed to unenroll factor:",_)}f("Two-factor authentication disabled successfully"),k(!1)}catch(j){n(j instanceof Error?j.message:"Failed to disable 2FA")}finally{c(!1)}}},E=j=>{navigator.clipboard.writeText(j),f("Copied to clipboard!"),setTimeout(()=>f(null),2e3)},F=()=>{m(!1),o(null),N(""),n(null),f(null)};return w&&h?e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-6",children:[e.jsx(Y,{className:"w-6 h-6 text-primary-400"}),e.jsx("h4",{className:"text-lg font-medium text-white",children:"Setup Two-Factor Authentication"})]}),b&&e.jsx("div",{className:"mb-4 bg-red-500/20 border border-red-500/30 rounded-lg p-3",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx($,{className:"w-4 h-4 text-red-400"}),e.jsx("span",{className:"text-red-400 text-sm",children:b})]})}),d&&e.jsx("div",{className:"mb-4 bg-green-500/20 border border-green-500/30 rounded-lg p-3",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(L,{className:"w-4 h-4 text-green-400"}),e.jsx("span",{className:"text-green-400 text-sm",children:d})]})}),r?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx(L,{className:"w-16 h-16 text-green-400 mx-auto mb-4"}),e.jsx("h5",{className:"text-lg font-medium text-white mb-2",children:"2FA Enabled Successfully!"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Your account is now protected with two-factor authentication."})]}),e.jsxs("div",{className:"bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4",children:[e.jsx("h5",{className:"font-medium text-yellow-400 mb-2",children:"Important: Save Your Backup Codes"}),e.jsx("p",{className:"text-yellow-300 text-sm mb-4",children:"Store these backup codes in a safe place. You can use them to access your account if you lose your authenticator device."}),e.jsx("div",{className:"grid grid-cols-2 gap-2 mb-4",children:h.backupCodes.map((j,I)=>e.jsx("div",{className:"bg-background-secondary border border-border-primary rounded p-2 text-center",children:e.jsx("code",{className:"text-primary-400 font-mono",children:j})},I))}),e.jsxs(x,{onClick:()=>E(h.backupCodes.join(`
`)),variant:"secondary",size:"sm",children:[e.jsx(K,{className:"w-4 h-4 mr-2"}),"Copy All Codes"]})]}),e.jsx(x,{onClick:()=>p(!1),variant:"primary",className:"w-full",children:"I've Saved My Backup Codes"})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h5",{className:"font-medium text-white mb-2",children:"Step 1: Scan QR Code"}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:"Scan this QR code with your authenticator app (Google Authenticator, Authy, etc.)"}),e.jsx("div",{className:"bg-white p-4 rounded-lg inline-block",children:e.jsx("img",{src:h.qrCode,alt:"2FA QR Code",className:"w-48 h-48"})})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-white mb-2",children:"Step 2: Manual Entry (Alternative)"}),e.jsx("p",{className:"text-gray-400 text-sm mb-3",children:"If you can't scan the QR code, enter this secret key manually:"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"flex-1 bg-background-secondary border border-border-primary rounded-lg p-3",children:e.jsx("code",{className:"text-primary-400 font-mono",children:s?h.secret:"••••••••••••••••"})}),e.jsx(x,{onClick:()=>t(!s),variant:"secondary",size:"sm",children:s?e.jsx(B,{className:"w-4 h-4"}):e.jsx(U,{className:"w-4 h-4"})}),e.jsx(x,{onClick:()=>E(h.secret),variant:"secondary",size:"sm",children:e.jsx(K,{className:"w-4 h-4"})})]})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-white mb-2",children:"Step 3: Verify Setup"}),e.jsx("p",{className:"text-gray-400 text-sm mb-3",children:"Enter the 6-digit code from your authenticator app:"}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(O,{value:u,onChange:N,placeholder:"123456",className:"flex-1"}),e.jsx(x,{onClick:C,isLoading:y,disabled:u.length!==6,children:"Verify"})]})]}),e.jsx("div",{className:"flex space-x-3",children:e.jsx(x,{onClick:F,variant:"secondary",className:"flex-1",children:"Cancel"})})]})]}):e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(Y,{className:`w-6 h-6 ${a?"text-green-400":"text-gray-400"}`}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-medium text-white",children:"Two-Factor Authentication"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Add an extra layer of security to your account"})]})]}),e.jsx("div",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${a?"bg-green-500/20 text-green-400":"bg-gray-500/20 text-gray-400"}`,children:a?"Enabled":"Disabled"})]}),b&&e.jsx("div",{className:"mb-4 bg-red-500/20 border border-red-500/30 rounded-lg p-3",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx($,{className:"w-4 h-4 text-red-400"}),e.jsx("span",{className:"text-red-400 text-sm",children:b})]})}),d&&e.jsx("div",{className:"mb-4 bg-green-500/20 border border-green-500/30 rounded-lg p-3",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(L,{className:"w-4 h-4 text-green-400"}),e.jsx("span",{className:"text-green-400 text-sm",children:d})]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-gray-300 text-sm",children:a?"Two-factor authentication is currently enabled for your account. You can disable it below if needed.":"Enable two-factor authentication to add an extra layer of security to your account. You'll need an authenticator app like Google Authenticator or Authy."}),e.jsx("div",{className:"flex space-x-3",children:a?e.jsx(x,{onClick:A,isLoading:y,variant:"danger",children:"Disable 2FA"}):e.jsxs(x,{onClick:v,isLoading:y,variant:"primary",children:[e.jsx(X,{className:"w-4 h-4 mr-2"}),"Enable 2FA"]})})]})]})},ve=({isOpen:a,onClose:k,onSuccess:w})=>{const[m,y]=l.useState({currentPassword:"",newPassword:"",confirmPassword:""}),[c,b]=l.useState({current:!1,new:!1,confirm:!1}),[n,d]=l.useState(!1),[f,h]=l.useState(null),[o,u]=l.useState(null),N=(A,E)=>{y(F=>({...F,[A]:E})),h(null)},s=A=>E=>{N(A,E)},t=A=>{b(E=>({...E,[A]:!E[A]}))},r=()=>m.currentPassword?m.newPassword?m.newPassword.length<8?(h("New password must be at least 8 characters long"),!1):m.newPassword!==m.confirmPassword?(h("New passwords do not match"),!1):m.currentPassword===m.newPassword?(h("New password must be different from current password"),!1):!0:(h("New password is required"),!1):(h("Current password is required"),!1),p=async A=>{if(A.preventDefault(),!!r()){d(!0),h(null),u(null);try{const E=localStorage.getItem("auth_token"),F=await fetch("/api/auth/change-password",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${E}`},body:JSON.stringify({currentPassword:m.currentPassword,newPassword:m.newPassword})}),j=await F.json();if(!F.ok)throw new Error(j.error||"Failed to change password");u("Password changed successfully!"),setTimeout(()=>{w(),k(),v()},1500)}catch(E){h(E instanceof Error?E.message:"Failed to change password")}finally{d(!1)}}},v=()=>{y({currentPassword:"",newPassword:"",confirmPassword:""}),b({current:!1,new:!1,confirm:!1}),h(null),u(null)},C=()=>{v(),k()};return a?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-background-secondary rounded-lg border border-border-primary w-full max-w-md",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(X,{className:"w-5 h-5 text-primary-400"}),e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Change Password"})]}),e.jsx("button",{onClick:C,className:"text-gray-400 hover:text-white transition-colors",children:e.jsx(Q,{className:"w-5 h-5"})})]}),e.jsxs("form",{onSubmit:p,className:"p-6 space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Current Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(O,{type:c.current?"text":"password",value:m.currentPassword,onChange:s("currentPassword"),placeholder:"Enter your current password",className:"pr-10",disabled:n}),e.jsx("button",{type:"button",onClick:()=>t("current"),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white",children:c.current?e.jsx(B,{className:"w-4 h-4"}):e.jsx(U,{className:"w-4 h-4"})})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"New Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(O,{type:c.new?"text":"password",value:m.newPassword,onChange:s("newPassword"),placeholder:"Enter your new password",className:"pr-10",disabled:n}),e.jsx("button",{type:"button",onClick:()=>t("new"),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white",children:c.new?e.jsx(B,{className:"w-4 h-4"}):e.jsx(U,{className:"w-4 h-4"})})]}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Password must be at least 8 characters long"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Confirm New Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(O,{type:c.confirm?"text":"password",value:m.confirmPassword,onChange:s("confirmPassword"),placeholder:"Confirm your new password",className:"pr-10",disabled:n}),e.jsx("button",{type:"button",onClick:()=>t("confirm"),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white",children:c.confirm?e.jsx(B,{className:"w-4 h-4"}):e.jsx(U,{className:"w-4 h-4"})})]})]}),f&&e.jsx("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-3",children:e.jsx("p",{className:"text-red-400 text-sm",children:f})}),o&&e.jsx("div",{className:"bg-green-500/10 border border-green-500/30 rounded-lg p-3",children:e.jsx("p",{className:"text-green-400 text-sm",children:o})}),e.jsxs("div",{className:"flex space-x-3 pt-4",children:[e.jsx(x,{type:"button",variant:"secondary",onClick:C,disabled:n,className:"flex-1",children:"Cancel"}),e.jsx(x,{type:"submit",variant:"primary",isLoading:n,disabled:n,className:"flex-1",children:n?"Changing...":"Change Password"})]})]})]})}):null},Se=({isOpen:a,onClose:k,action:w})=>{const[m,y]=l.useState(""),[c,b]=l.useState(!1),[n,d]=l.useState(null),[f,h]=l.useState("confirm"),u={deactivate:{title:"Deactivate Account",description:"Your account will be temporarily disabled. You can reactivate it by logging in again.",confirmText:"DEACTIVATE",buttonText:"Deactivate Account",warningText:"This will temporarily disable your account and log you out.",endpoint:"/api/auth/deactivate-account"},delete:{title:"Delete Account",description:"This will permanently delete your account and all associated data. This action cannot be undone.",confirmText:"DELETE FOREVER",buttonText:"Delete Account Forever",warningText:"This will permanently delete all your data including study sets, flashcards, progress, and subscription information.",endpoint:"/api/auth/delete-account"}}[w],N=m===u.confirmText,s=async()=>{if(f==="confirm"){h("final");return}if(!N){d(`Please type "${u.confirmText}" to confirm`);return}b(!0),d(null);try{const p=localStorage.getItem("auth_token"),v=await fetch(u.endpoint,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${p}`},body:JSON.stringify({confirmation:m})}),C=await v.json();if(!v.ok)throw new Error(C.error||`Failed to ${w} account`);localStorage.removeItem("auth_token"),localStorage.removeItem("user_data"),window.location.href="/login"}catch(p){d(p instanceof Error?p.message:`Failed to ${w} account`)}finally{b(!1)}},t=()=>{y(""),d(null),h("confirm"),k()},r=()=>{h("confirm"),d(null)};return a?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-background-secondary rounded-lg border border-red-500/30 w-full max-w-md",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-red-500/30",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx($,{className:"w-5 h-5 text-red-400"}),e.jsx("h3",{className:"text-lg font-semibold text-white",children:u.title})]}),e.jsx("button",{onClick:t,className:"text-gray-400 hover:text-white transition-colors",children:e.jsx(Q,{className:"w-5 h-5"})})]}),e.jsx("div",{className:"p-6",children:f==="confirm"?e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx($,{className:"w-5 h-5 text-red-400 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-red-400 mb-2",children:"Warning"}),e.jsx("p",{className:"text-gray-300 text-sm",children:u.warningText})]})]})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-gray-300 text-sm mb-4",children:u.description}),w==="delete"&&e.jsxs("div",{className:"space-y-2 text-sm text-gray-400",children:[e.jsx("p",{children:"This will delete:"}),e.jsxs("ul",{className:"list-disc list-inside space-y-1 ml-4",children:[e.jsx("li",{children:"All study sets and flashcards"}),e.jsx("li",{children:"Quiz history and progress"}),e.jsx("li",{children:"Account settings and preferences"}),e.jsx("li",{children:"Subscription and billing information"}),e.jsx("li",{children:"All uploaded documents"})]})]})]}),e.jsxs("div",{className:"flex space-x-3 pt-4",children:[e.jsx(x,{variant:"secondary",onClick:t,className:"flex-1",children:"Cancel"}),e.jsx(x,{variant:"danger",onClick:s,className:"flex-1",children:"Continue"})]})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(se,{className:"w-5 h-5 text-red-400"}),e.jsx("h4",{className:"font-medium text-red-400",children:"Final Confirmation"})]})}),e.jsxs("div",{children:[e.jsxs("p",{className:"text-gray-300 text-sm mb-4",children:["To confirm this action, please type ",e.jsx("span",{className:"font-mono font-bold text-red-400",children:u.confirmText})," in the box below:"]}),e.jsx(O,{type:"text",value:m,onChange:p=>{y(p),d(null)},placeholder:u.confirmText,className:"font-mono",disabled:c})]}),n&&e.jsx("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-3",children:e.jsx("p",{className:"text-red-400 text-sm",children:n})}),e.jsxs("div",{className:"flex space-x-3 pt-4",children:[e.jsx(x,{variant:"secondary",onClick:r,disabled:c,className:"flex-1",children:"Back"}),e.jsx(x,{variant:"danger",onClick:s,isLoading:c,disabled:c||!N,className:"flex-1",children:c?"Processing...":u.buttonText})]})]})})]})}):null},ke=[{id:"profile",label:"Profile",icon:de,description:"Manage your account information"},{id:"preferences",label:"Preferences",icon:me,description:"Customize your experience"},{id:"notifications",label:"Notifications",icon:ue,description:"Control notification settings"},{id:"security",label:"Security",icon:Y,description:"Password and security settings"},{id:"subscription",label:"Subscription",icon:R,description:"Manage your subscription plan"},{id:"billing",label:"Billing",icon:R,description:"Payment history and invoices"},{id:"data",label:"Data Management",icon:xe,description:"Export, import, and manage your data"}],Ae=()=>{const[a,k]=l.useState("profile"),[w,m]=l.useState(!1),[y,c]=l.useState(null),[b,n]=l.useState(null),{user:d}=oe(),[f,h]=l.useState({name:(d==null?void 0:d.name)||"",email:(d==null?void 0:d.email)||"",bio:"",avatar:null}),[o,u]=l.useState({theme:"dark",language:"en",studyReminders:!0,autoSave:!0,defaultStudyMode:"flashcards",sessionDuration:30,difficultyLevel:ge.MEDIUM}),[N,s]=l.useState({emailNotifications:!0,studyReminders:!0,weeklyProgress:!1,marketingEmails:!1,achievementNotifications:!0,streakReminders:!0}),[t,r]=l.useState({twoFactorEnabled:!1,loginNotifications:!0,sessionTimeout:30}),[p,v]=l.useState(!1),[C,A]=l.useState(!1),[E,F]=l.useState("deactivate");l.useEffect(()=>{(async()=>{try{m(!0);const S=localStorage.getItem("auth_token");if(!S){c("Authentication required");return}const g=await fetch("/api/user/preferences",{headers:{Authorization:`Bearer ${S}`}});if(!g.ok)throw new Error("Failed to load user preferences");const D=await g.json();if(D.success&&D.data){const T=D.data;u({theme:T.theme,language:T.language,studyReminders:T.study_reminders,autoSave:T.auto_save,defaultStudyMode:T.default_study_mode,sessionDuration:T.session_duration,difficultyLevel:T.difficulty_level})}}catch(S){c("Failed to load user settings"),console.error("Load user settings error:",S)}finally{m(!1)}})()},[]);const j=async()=>{m(!0),c(null),n(null);try{const i=new FormData;i.append("name",f.name),i.append("bio",f.bio),f.avatar&&i.append("avatar",f.avatar);const S=localStorage.getItem("auth_token"),g=await fetch("/api/user/profile",{method:"PUT",headers:{Authorization:`Bearer ${S}`},body:i});if(!g.ok)throw new Error("Failed to update profile");const D=await g.json();if(D.success)n("Profile updated successfully!");else throw new Error(D.error)}catch(i){c(i instanceof Error?i.message:"Failed to update profile")}finally{m(!1)}},I=async()=>{m(!0),c(null),n(null);try{const i=localStorage.getItem("auth_token"),S={theme:o.theme,language:o.language,study_reminders:o.studyReminders,auto_save:o.autoSave,default_study_mode:o.defaultStudyMode,session_duration:o.sessionDuration,difficulty_level:o.difficultyLevel},g=await fetch("/api/user/preferences",{method:"PUT",headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"},body:JSON.stringify(S)});if(!g.ok)throw new Error("Failed to update preferences");const D=await g.json();if(D.success)n("Preferences updated successfully!");else throw new Error(D.error)}catch(i){c(i instanceof Error?i.message:"Failed to update preferences")}finally{m(!1)}},M=async()=>{m(!0),c(null),n(null);try{const i=localStorage.getItem("auth_token"),S=await fetch("/api/user/notifications",{method:"PUT",headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"},body:JSON.stringify(N)});if(!S.ok)throw new Error("Failed to update notification settings");const g=await S.json();if(g.success)n("Notification settings updated successfully!");else throw new Error(g.error)}catch(i){c(i instanceof Error?i.message:"Failed to update notification settings")}finally{m(!1)}},P=()=>{var i,S;return e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Profile Information"}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Profile Picture"}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white text-2xl font-bold",children:((S=(i=d==null?void 0:d.name)==null?void 0:i.charAt(0))==null?void 0:S.toUpperCase())||"U"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(x,{variant:"secondary",size:"sm",onClick:()=>{const g=document.createElement("input");g.type="file",g.accept="image/*",g.onchange=D=>{var q;const T=(q=D.target.files)==null?void 0:q[0];T&&h({...f,avatar:T})},g.click()},children:[e.jsx(he,{className:"w-4 h-4 mr-2"}),"Upload Photo"]}),e.jsx("p",{className:"text-xs text-gray-500",children:"JPG, PNG up to 5MB"})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(O,{label:"Full Name",value:f.name,onChange:g=>h({...f,name:g}),placeholder:"Enter your full name"}),e.jsx(O,{label:"Email Address",type:"email",value:f.email,onChange:g=>h({...f,email:g}),placeholder:"Enter your email",disabled:!0}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Bio (Optional)"}),e.jsx("textarea",{value:f.bio,onChange:g=>h({...f,bio:g.target.value}),placeholder:"Tell us about yourself...",rows:4,className:"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]})]}),e.jsx("div",{className:"mt-6",children:e.jsx(x,{onClick:j,isLoading:w,children:"Save Profile"})})]})})},z=()=>e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"App Preferences"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Theme"}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsxs("button",{onClick:()=>u({...o,theme:"dark"}),className:`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${o.theme==="dark"?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:[e.jsx(pe,{className:"w-4 h-4"}),e.jsx("span",{children:"Dark"})]}),e.jsxs("button",{onClick:()=>u({...o,theme:"light"}),className:`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${o.theme==="light"?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"}`,disabled:!0,children:[e.jsx(fe,{className:"w-4 h-4"}),e.jsx("span",{children:"Light (Coming Soon)"})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Language"}),e.jsxs("select",{value:o.language,onChange:i=>u({...o,language:i.target.value}),className:"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[e.jsx("option",{value:"en",children:"English"}),e.jsx("option",{value:"es",disabled:!0,children:"Spanish (Coming Soon)"}),e.jsx("option",{value:"fr",disabled:!0,children:"French (Coming Soon)"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Default Study Mode"}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("button",{onClick:()=>u({...o,defaultStudyMode:"flashcards"}),className:`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${o.defaultStudyMode==="flashcards"?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:e.jsx("span",{children:"Flashcards"})}),e.jsx("button",{onClick:()=>u({...o,defaultStudyMode:"quiz"}),className:`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${o.defaultStudyMode==="quiz"?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:e.jsx("span",{children:"Quiz"})})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Default Session Duration (minutes)"}),e.jsxs("select",{value:o.sessionDuration,onChange:i=>u({...o,sessionDuration:parseInt(i.target.value)}),className:"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[e.jsx("option",{value:15,children:"15 minutes"}),e.jsx("option",{value:30,children:"30 minutes"}),e.jsx("option",{value:45,children:"45 minutes"}),e.jsx("option",{value:60,children:"1 hour"}),e.jsx("option",{value:90,children:"1.5 hours"})]})]}),e.jsx(ye,{value:o.difficultyLevel,onChange:i=>u({...o,difficultyLevel:i}),label:"Default Difficulty Level",className:"bg-background-secondary rounded-lg p-4"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-300",children:"Study Reminders"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Get reminded to study regularly"})]}),e.jsx("button",{onClick:()=>u({...o,studyReminders:!o.studyReminders}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${o.studyReminders?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${o.studyReminders?"translate-x-6":"translate-x-1"}`})})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-300",children:"Auto-save"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Automatically save your progress"})]}),e.jsx("button",{onClick:()=>u({...o,autoSave:!o.autoSave}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${o.autoSave?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${o.autoSave?"translate-x-6":"translate-x-1"}`})})]})]})]}),e.jsx("div",{className:"mt-6",children:e.jsx(x,{onClick:I,isLoading:w,children:"Save Preferences"})})]})}),_=()=>e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Notification Settings"}),e.jsx("div",{className:"space-y-4",children:Object.entries(N).map(([i,S])=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-300 capitalize",children:i.replace(/([A-Z])/g," $1").trim()}),e.jsxs("p",{className:"text-xs text-gray-500",children:[i==="emailNotifications"&&"Receive important updates via email",i==="studyReminders"&&"Get reminded when it's time to study",i==="weeklyProgress"&&"Weekly summary of your study progress",i==="marketingEmails"&&"Product updates and tips",i==="achievementNotifications"&&"Get notified when you unlock achievements",i==="streakReminders"&&"Reminders to maintain your study streak"]})]}),e.jsx("button",{onClick:()=>s({...N,[i]:!S}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${S?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${S?"translate-x-6":"translate-x-1"}`})})]},i))}),e.jsx("div",{className:"mt-6",children:e.jsx(x,{onClick:M,isLoading:w,children:"Save Notification Settings"})})]})}),te=()=>e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Security Settings"}),e.jsxs("div",{className:"space-y-6",children:[e.jsx(Ne,{enabled:t.twoFactorEnabled,onToggle:i=>r({...t,twoFactorEnabled:i})}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-4 border border-border-primary",children:[e.jsx("h4",{className:"font-medium text-white mb-4",children:"Session Management"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-300",children:"Login Notifications"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Get notified when someone logs into your account"})]}),e.jsx("button",{onClick:()=>r({...t,loginNotifications:!t.loginNotifications}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${t.loginNotifications?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${t.loginNotifications?"translate-x-6":"translate-x-1"}`})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Session Timeout (minutes)"}),e.jsxs("select",{value:t.sessionTimeout,onChange:i=>r({...t,sessionTimeout:parseInt(i.target.value)}),className:"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[e.jsx("option",{value:15,children:"15 minutes"}),e.jsx("option",{value:30,children:"30 minutes"}),e.jsx("option",{value:60,children:"1 hour"}),e.jsx("option",{value:120,children:"2 hours"}),e.jsx("option",{value:480,children:"8 hours"}),e.jsx("option",{value:1440,children:"1 day"}),e.jsx("option",{value:10080,children:"1 week"}),e.jsx("option",{value:20160,children:"2 weeks"}),e.jsx("option",{value:30240,children:"3 weeks"}),e.jsx("option",{value:40320,children:"4 weeks"}),e.jsx("option",{value:50400,children:"5 weeks"}),e.jsx("option",{value:60480,children:"6 weeks"}),e.jsx("option",{value:70560,children:"7 weeks"}),e.jsx("option",{value:80640,children:"8 weeks"}),e.jsx("option",{value:0,children:"Never expire"})]})]})]})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-4 border border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-3",children:[e.jsx(X,{className:"w-5 h-5 text-primary-400"}),e.jsx("h4",{className:"font-medium text-white",children:"Change Password"})]}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:"Update your password to keep your account secure"}),e.jsx(x,{variant:"secondary",onClick:()=>v(!0),children:"Change Password"})]}),e.jsxs("div",{className:"bg-red-500/10 rounded-lg p-4 border border-red-500/30",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-3",children:[e.jsx($,{className:"w-5 h-5 text-red-400"}),e.jsx("h4",{className:"font-medium text-white",children:"Danger Zone"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-red-400 mb-2",children:"Account Deactivation"}),e.jsx("p",{className:"text-gray-400 text-sm mb-3",children:"Temporarily deactivate your account. You can reactivate it later."}),e.jsx(x,{variant:"secondary",size:"sm",onClick:()=>{F("deactivate"),A(!0)},children:"Deactivate Account"})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-red-400 mb-2",children:"Account Deletion"}),e.jsx("p",{className:"text-gray-400 text-sm mb-3",children:"Permanently delete your account and all associated data. This action cannot be undone."}),e.jsx(x,{variant:"danger",size:"sm",onClick:()=>{F("delete"),A(!0)},children:"Delete Account"})]})]})]})]})]})}),ae=()=>e.jsx(be,{}),re=()=>e.jsx(we,{}),ne=()=>e.jsx(je,{}),ie=()=>{switch(a){case"profile":return P();case"preferences":return z();case"notifications":return _();case"security":return te();case"subscription":return ae();case"billing":return re();case"data":return ne();default:return P()}};return e.jsxs("div",{className:"min-h-screen bg-background-primary text-white",children:[e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Settings"}),e.jsx("p",{className:"text-gray-400",children:"Manage your account and preferences"})]}),y&&e.jsxs("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx($,{className:"w-5 h-5 text-red-400"}),e.jsx("span",{className:"text-red-400 font-medium",children:"Error"})]}),e.jsx("p",{className:"text-red-300 mt-1",children:y}),e.jsx(x,{onClick:()=>c(null),variant:"secondary",size:"sm",className:"mt-2",children:"Dismiss"})]}),b&&e.jsxs("div",{className:"mb-6 bg-green-500/20 border border-green-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(L,{className:"w-5 h-5 text-green-400"}),e.jsx("span",{className:"text-green-400 font-medium",children:"Success"})]}),e.jsx("p",{className:"text-green-300 mt-1",children:b}),e.jsx(x,{onClick:()=>n(null),variant:"secondary",size:"sm",className:"mt-2",children:"Dismiss"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[e.jsx("div",{className:"lg:col-span-1",children:e.jsx("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:e.jsx("nav",{className:"space-y-2",children:ke.map(i=>{const S=i.icon,g=a===i.id;return e.jsxs("button",{onClick:()=>k(i.id),className:`
                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left
                        transition-all duration-200
                        ${g?"bg-primary-500/20 text-primary-400 border border-primary-500/30":"text-gray-300 hover:bg-background-tertiary hover:text-white"}
                      `,children:[e.jsx(S,{className:"w-5 h-5"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("span",{className:"font-medium block",children:i.label}),e.jsx("span",{className:"text-xs text-gray-500 block truncate",children:i.description})]})]},i.id)})})})}),e.jsx("div",{className:"lg:col-span-3",children:e.jsx(Z.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:ie()},a)})]})]}),e.jsx(ve,{isOpen:p,onClose:()=>v(!1),onSuccess:()=>{console.log("Password changed successfully")}}),e.jsx(Se,{isOpen:C,onClose:()=>A(!1),action:E})]})};export{Ae as SettingsPage};
