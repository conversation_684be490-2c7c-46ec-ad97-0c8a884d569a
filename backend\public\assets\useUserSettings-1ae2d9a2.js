import{r as o}from"./index-8171bd56.js";const w=()=>{const[u,i]=o.useState(null),[l,c]=o.useState(!0),[g,s]=o.useState(null),a=o.useCallback(async()=>{try{c(!0),s(null);const e=localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token");if(!e)throw new Error("No authentication token found");const t=await fetch("/api/user/settings",{headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!t.ok){const n=await t.json();throw new Error(n.error||"Failed to fetch user settings")}const r=await t.json();i(r.data)}catch(e){s(e.message),console.error("Error fetching user settings:",e)}finally{c(!1)}},[]),h=o.useCallback(async e=>{try{s(null);const t=localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token");if(!t)throw new Error("No authentication token found");const r=await fetch("/api/user/settings",{method:"PATCH",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify(e)});if(!r.ok){const d=await r.json();throw new Error(d.error||"Failed to update user settings")}const n=await r.json();i(n.data)}catch(t){throw s(t.message),console.error("Error updating user settings:",t),t}},[]),f=o.useCallback(async()=>{await a()},[a]);return o.useEffect(()=>{a()},[a]),{settings:u,loading:l,error:g,updateSettings:h,refetch:f}};export{w as u};
