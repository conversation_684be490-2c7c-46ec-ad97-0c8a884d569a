import{r as u,j as e,c as ve,B as S,a as se,b as ee,H as je,d as we,e as Ne,u as _e}from"./index-dc82a028.js";import{u as ie}from"./studyStore-483ccf55.js";import{u as ke}from"./documentStore-2a62df30.js";import{C as M,D as le,a as oe,d as te,n as ce,b as de}from"./DifficultySelector-89f34ac4.js";import{u as ue}from"./useUserSettings-db2c61ce.js";const me=({selectedDocuments:s,onSelectionChange:x,maxSelection:i=5})=>{const{documents:g,fetchDocuments:o,isLoading:y}=ke(),[a,F]=u.useState("");u.useEffect(()=>{g.length===0&&o()},[g.length,o]);const j=g.filter(l=>l.is_processed&&l.filename.toLowerCase().includes(a.toLowerCase())),E=l=>{s.includes(l)?x(s.filter(b=>b!==l)):s.length<i&&x([...s,l])},T=()=>g.filter(l=>s.includes(l.id));return y?e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx("div",{className:"text-gray-400",children:"Loading documents..."})}):g.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"text-gray-400 mb-4",children:"No documents found"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Upload some documents first to generate study materials."})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{children:e.jsx("input",{type:"text",placeholder:"Search documents...",value:a,onChange:l=>F(l.target.value),className:"w-full px-3 py-2 bg-background-secondary border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"})}),s.length>0&&e.jsxs("div",{className:"bg-primary-500/10 border border-primary-500/30 rounded-lg p-3",children:[e.jsxs("div",{className:"text-sm text-primary-400 mb-2",children:["Selected ",s.length," of ",i," documents:"]}),e.jsx("div",{className:"space-y-1",children:T().map(l=>e.jsxs("div",{className:"text-sm text-gray-300 flex items-center justify-between",children:[e.jsx("span",{className:"truncate",children:l.filename}),e.jsx("button",{onClick:()=>E(l.id),className:"text-red-400 hover:text-red-300 ml-2",children:"✕"})]},l.id))})]}),e.jsx("div",{className:"max-h-64 overflow-y-auto space-y-2",children:j.map(l=>{const _=s.includes(l.id),b=!_&&s.length<i;return e.jsx("div",{className:`
                p-3 rounded-lg border cursor-pointer transition-all
                ${_?"bg-primary-500/20 border-primary-500":b?"bg-background-secondary border-gray-600 hover:border-gray-500":"bg-gray-800 border-gray-700 opacity-50 cursor-not-allowed"}
              `,onClick:()=>b||_?E(l.id):null,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex-1 min-w-0",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-lg",children:l.file_type==="pdf"?"📄":l.file_type==="docx"?"📝":l.file_type==="txt"?"📃":"📊"}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("p",{className:"text-white font-medium truncate",children:l.filename}),e.jsxs("p",{className:"text-sm text-gray-400",children:[l.file_type.toUpperCase()," • ",Math.round(l.file_size/1024)," KB"]})]})]})}),e.jsx("div",{className:`
                  w-5 h-5 rounded border-2 flex items-center justify-center
                  ${_?"bg-primary-500 border-primary-500":"border-gray-500"}
                `,children:_&&e.jsx("svg",{className:"w-3 h-3 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]})},l.id)})}),j.length===0&&a&&e.jsx("div",{className:"text-center py-4 text-gray-400",children:"No documents match your search."})]})},Se=[{value:M.SHORT,label:"Short",description:"Concise answers (1-2 sentences)",icon:"📝"},{value:M.MEDIUM,label:"Medium",description:"Balanced detail (2-3 sentences)",icon:"📄"},{value:M.LONG,label:"Long",description:"Comprehensive answers (3-5 sentences)",icon:"📋"}],Ce=s=>{switch(s){case M.SHORT:return"bg-background-secondary text-text-primary border-emerald-500/30 hover:bg-emerald-500/10 hover:border-emerald-500/50";case M.MEDIUM:return"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50";case M.LONG:return"bg-background-secondary text-text-primary border-indigo-500/30 hover:bg-indigo-500/10 hover:border-indigo-500/50";default:return"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50"}},Ee=s=>{switch(s){case M.SHORT:return"bg-emerald-500/20 text-emerald-300 border-emerald-500 shadow-lg shadow-emerald-500/20";case M.MEDIUM:return"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20";case M.LONG:return"bg-indigo-500/20 text-indigo-300 border-indigo-500 shadow-lg shadow-indigo-500/20";default:return"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20"}},he=({value:s,onChange:x,className:i="",disabled:g=!1,label:o="Content Length"})=>e.jsxs("div",{className:`space-y-3 ${i}`,children:[e.jsx("label",{className:"block text-sm font-medium text-text-primary",children:o}),e.jsx("div",{className:"grid grid-cols-3 gap-3",children:Se.map(y=>{const a=s===y.value,F=a?Ee(y.value):Ce(y.value);return e.jsxs("button",{type:"button",onClick:()=>!g&&x(y.value),disabled:g,className:`
                relative p-4 rounded-lg border-2 text-sm font-medium transition-all duration-200 transform-gpu
                ${F}
                ${g?"opacity-50 cursor-not-allowed":"cursor-pointer hover:scale-105"}
                ${a?"ring-2 ring-offset-2 ring-primary-500 ring-offset-background-primary":""}
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-background-primary
              `,title:y.description,"aria-pressed":a,children:[e.jsxs("div",{className:"text-center space-y-2",children:[e.jsx("div",{className:"text-2xl",children:y.icon}),e.jsx("div",{className:"font-semibold",children:y.label}),e.jsx("div",{className:`text-xs ${a?"text-white/90":"text-text-secondary"}`,children:y.description})]}),a&&e.jsx("div",{className:"absolute top-2 right-2",children:e.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]},y.value)})}),e.jsx("p",{className:"text-xs text-text-muted",children:"Choose how detailed you want the flashcard answers to be. This affects the length and depth of explanations."})]}),Ae=ve(s=>({isGenerating:!1,generationProgress:"",lastGenerated:null,generateFlashcards:async x=>{s({isGenerating:!0,generationProgress:"Preparing documents..."});try{const i=localStorage.getItem("auth_token");s({generationProgress:"Generating flashcards with AI..."});const g=await fetch("/api/ai/generate-flashcards",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${i}`},body:JSON.stringify(x)});if(!g.ok){const y=await g.json();throw new Error(y.error||"Generation failed")}const o=await g.json();if(o.success)return s({lastGenerated:{studySet:o.data.studySet,content:o.data.flashcards,type:"flashcards"},isGenerating:!1,generationProgress:""}),{studySet:o.data.studySet,flashcards:o.data.flashcards,creditsRemaining:o.data.creditsRemaining};throw new Error(o.error)}catch(i){throw s({isGenerating:!1,generationProgress:""}),i}},generateQuiz:async x=>{s({isGenerating:!0,generationProgress:"Preparing documents..."});try{const i=localStorage.getItem("auth_token");s({generationProgress:"Generating quiz questions with AI..."});const g=await fetch("/api/ai/generate-quiz",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${i}`},body:JSON.stringify(x)});if(!g.ok){const y=await g.json();throw new Error(y.error||"Generation failed")}const o=await g.json();if(o.success)return s({lastGenerated:{studySet:o.data.studySet,content:o.data.questions,type:"quiz"},isGenerating:!1,generationProgress:""}),{studySet:o.data.studySet,questions:o.data.questions,creditsRemaining:o.data.creditsRemaining};throw new Error(o.error)}catch(i){throw s({isGenerating:!1,generationProgress:""}),i}},generateMoreFlashcards:async x=>{s({isGenerating:!0,generationProgress:"Preparing documents..."});try{const i=localStorage.getItem("auth_token");s({generationProgress:"Generating additional flashcards with AI..."});const g=await fetch("/api/ai/generate-more-flashcards",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${i}`},body:JSON.stringify(x)});if(!g.ok){const y=await g.json();throw new Error(y.error||"Generation failed")}const o=await g.json();if(o.success)return s({isGenerating:!1,generationProgress:""}),{flashcards:o.data.flashcards,creditsRemaining:o.data.creditsRemaining};throw new Error(o.error)}catch(i){throw s({isGenerating:!1,generationProgress:""}),i}},clearLastGenerated:()=>{s({lastGenerated:null})}})),Fe=({selectedCount:s,totalCount:x,onDeleteSelected:i,onClearSelection:g,isLoading:o=!1,className:y=""})=>s===0?null:e.jsx("div",{className:`bg-gray-800 border border-gray-700 rounded-lg p-4 mb-4 ${y}`,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("span",{className:"text-white font-medium",children:[s," of ",x," flashcard",s!==1?"s":""," selected"]}),e.jsx("button",{onClick:g,className:"text-gray-400 hover:text-white text-sm underline",disabled:o,children:"Clear selection"})]}),e.jsx("div",{className:"flex items-center space-x-3",children:e.jsx(S,{onClick:i,variant:"danger",size:"sm",isLoading:o,disabled:o,className:"px-4 py-2",children:"Delete Selected"})})]})}),$e=({studySetId:s,flashcards:x,onFlashcardAdded:i,onFlashcardUpdated:g,onFlashcardDeleted:o,onFlashcardsGenerated:y})=>{const{alert:a,confirm:F}=se(),{user:j}=ee(),{generateMoreFlashcards:E}=Ae(),{settings:T,updateSettings:l}=ue(),[_,b]=u.useState(!1),[z,L]=u.useState([]),[I,D]=u.useState(10),[O,C]=u.useState(""),[P,X]=u.useState(le.MEDIUM),[V,K]=u.useState(M.MEDIUM),[H,m]=u.useState(!1),[A,R]=u.useState(!1),[$,c]=u.useState({front:"",back:"",difficulty_level:3}),[w,Y]=u.useState(null),[G,B]=u.useState({front:"",back:"",difficulty_level:3}),[N,Q]=u.useState([]),[J,d]=u.useState(!1),[f,q]=u.useState(!1),[Z,t]=u.useState(!1),n=(r,p)=>{p?Q(k=>[...k,r]):(Q(k=>k.filter(U=>U!==r)),d(!1))},h=r=>{d(r),Q(r?x.map(p=>p.id):[])},v=()=>{Q([]),d(!1)},W=async()=>{if(N.length!==0){if(!(T!=null&&T.skip_delete_confirmations)){let r=!1;if(!await F({title:"Delete Flashcards",message:`Are you sure you want to delete ${N.length} flashcard${N.length!==1?"s":""}?`,variant:"danger",confirmText:"Delete",cancelText:"Cancel",buttonLayout:"corners",showNeverAskAgain:!0,onNeverAskAgainChange:k=>{r=k}}))return;if(r)try{await l({skip_delete_confirmations:!0})}catch(k){console.error("Failed to update user settings:",k)}}await re()}},re=async()=>{q(!0);try{const r=await fetch("/api/flashcards/bulk-delete",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")}`,"Content-Type":"application/json"},body:JSON.stringify({flashcardIds:N})});if(!r.ok){const U=await r.json();throw new Error(U.error||"Failed to delete flashcards")}const p=await r.json(),{deletedCount:k}=p.data;N.forEach(U=>o(U)),v(),await a({title:"Success",message:`${k} flashcard${k!==1?"s":""} deleted successfully!`,variant:"success"})}catch(r){await a({title:"Error",message:r.message||"Failed to delete flashcards",variant:"error"})}finally{q(!1)}},xe=async()=>{if(!$.front.trim()||!$.back.trim()){await a({title:"Validation Error",message:"Both front and back content are required.",variant:"error"});return}try{const r=await fetch(`/api/flashcards/study-set/${s}`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({front:$.front.trim(),back:$.back.trim(),difficulty_level:$.difficulty_level,is_ai_generated:!1})});if(!r.ok)throw new Error("Failed to create flashcard");const p=await r.json();i(p.data),c({front:"",back:"",difficulty_level:3}),R(!1),await a({title:"Success",message:"Flashcard added successfully!",variant:"success"})}catch(r){await a({title:"Error",message:r.message||"Failed to add flashcard",variant:"error"})}},ge=r=>{Y(r),B({front:r.front,back:r.back,difficulty_level:typeof r.difficulty_level=="string"?de(r.difficulty_level):r.difficulty_level||3})},pe=async()=>{if(w){if(!G.front.trim()||!G.back.trim()){await a({title:"Validation Error",message:"Both front and back content are required.",variant:"error"});return}try{const r=await fetch(`/api/flashcards/${w.id}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({front:G.front.trim(),back:G.back.trim(),difficulty_level:G.difficulty_level})});if(!r.ok)throw new Error("Failed to update flashcard");const p=await r.json();g(p.data),Y(null),B({front:"",back:"",difficulty_level:3}),await a({title:"Success",message:"Flashcard updated successfully!",variant:"success"})}catch(r){await a({title:"Error",message:r.message||"Failed to update flashcard",variant:"error"})}}},ye=()=>{Y(null),B({front:"",back:"",difficulty_level:3})},fe=async r=>{if(T!=null&&T.skip_delete_confirmations){await ae(r);return}let p=!1;if(await F({title:"Delete Flashcard",message:`Are you sure you want to delete this flashcard?

Front: ${r.front.substring(0,50)}${r.front.length>50?"...":""}`,variant:"danger",confirmText:"Delete",cancelText:"Cancel",buttonLayout:"corners",showNeverAskAgain:!0,onNeverAskAgainChange:U=>{p=U}})){if(p)try{await l({skip_delete_confirmations:!0})}catch(U){console.error("Failed to update user settings:",U)}await ae(r)}},ae=async r=>{try{if(!(await fetch(`/api/flashcards/${r.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")}`}})).ok)throw new Error("Failed to delete flashcard");o(r.id),await a({title:"Success",message:"Flashcard deleted successfully!",variant:"success"})}catch(p){await a({title:"Error",message:p.message||"Failed to delete flashcard",variant:"error"})}},ne=()=>Math.ceil(I/5),be=async()=>{if(z.length===0){await a({title:"No Documents Selected",message:"Please select at least one document to generate flashcards from.",variant:"warning"});return}const r=ne();if(j&&j.credits_remaining<r){await a({title:"Insufficient Credits",message:`You need ${r} credits to generate ${I} flashcards, but you only have ${j.credits_remaining} credits remaining.`,variant:"error"});return}if(await F({title:"Generate Flashcards",message:`Generate ${I} flashcards from ${z.length} document(s)?

This will cost ${r} credits.`,confirmText:"Generate",cancelText:"Cancel"})){m(!0);try{const k=await E({studySetId:s,documentIds:z,count:I,customPrompt:O.trim()||void 0,difficultyLevel:P,contentLength:V});y(k.flashcards),j&&ee.getState().updateUser({credits_remaining:k.creditsRemaining}),await a({title:"Success",message:`Generated ${k.flashcards.length} flashcards successfully!`,variant:"success"}),L([]),C(""),b(!1)}catch(k){await a({title:"Generation Error",message:k.message||"Failed to generate flashcards",variant:"error"})}finally{m(!1)}}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium text-white",children:"Manage Flashcards"}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(S,{onClick:()=>R(!A),variant:"secondary",size:"sm",children:"➕ Add Flashcard"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"AI Mode"}),e.jsx("button",{onClick:()=>b(!_),className:`
                relative inline-flex h-6 w-11 items-center rounded-full transition-colors
                ${_?"bg-primary-500":"bg-gray-600"}
              `,children:e.jsx("span",{className:`
                  inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                  ${_?"translate-x-6":"translate-x-1"}
                `})})]})]})]}),A&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"Add New Flashcard"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Front (Question/Term)"}),e.jsx("textarea",{value:$.front,onChange:r=>c(p=>({...p,front:r.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter the front content..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Back (Answer/Definition)"}),e.jsx("textarea",{value:$.back,onChange:r=>c(p=>({...p,back:r.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter the back content..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Difficulty Level"}),e.jsxs("select",{value:$.difficulty_level,onChange:r=>c(p=>({...p,difficulty_level:parseInt(r.target.value)})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:1,children:"1 - Very Easy"}),e.jsx("option",{value:2,children:"2 - Easy"}),e.jsx("option",{value:3,children:"3 - Medium"}),e.jsx("option",{value:4,children:"4 - Hard"}),e.jsx("option",{value:5,children:"5 - Very Hard"})]})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(S,{onClick:xe,variant:"primary",children:"Add Flashcard"}),e.jsx(S,{onClick:()=>R(!1),variant:"secondary",children:"Cancel"})]})]})]}),w&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"Edit Flashcard"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Front (Question/Term)"}),e.jsx("textarea",{value:G.front,onChange:r=>B(p=>({...p,front:r.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter the front content..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Back (Answer/Definition)"}),e.jsx("textarea",{value:G.back,onChange:r=>B(p=>({...p,back:r.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter the back content..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Difficulty Level"}),e.jsxs("select",{value:G.difficulty_level,onChange:r=>B(p=>({...p,difficulty_level:parseInt(r.target.value)})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:1,children:"1 - Very Easy"}),e.jsx("option",{value:2,children:"2 - Easy"}),e.jsx("option",{value:3,children:"3 - Medium"}),e.jsx("option",{value:4,children:"4 - Hard"}),e.jsx("option",{value:5,children:"5 - Very Hard"})]})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(S,{onClick:pe,variant:"primary",children:"Save Changes"}),e.jsx(S,{onClick:ye,variant:"secondary",children:"Cancel"})]})]})]}),_&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"AI Flashcard Generation"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Select Documents"}),e.jsx(me,{selectedDocuments:z,onSelectionChange:L,maxSelection:5})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Number of Flashcards"}),e.jsx("input",{type:"number",min:"1",max:"50",value:I,onChange:r=>D(parseInt(r.target.value)||1),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Credit Cost"}),e.jsxs("div",{className:"px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-primary-400 font-medium",children:[ne()," credits"]})]})]}),e.jsx(oe,{value:P,onChange:X}),e.jsx(he,{value:V,onChange:K}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Custom Instructions (Optional)"}),e.jsx("textarea",{value:O,onChange:r=>C(r.target.value),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Add specific instructions for flashcard generation..."})]}),e.jsx(S,{onClick:be,disabled:z.length===0||H,className:"w-full",variant:"primary",children:H?"Generating...":`Generate ${I} Flashcards`})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("h4",{className:"text-md font-medium text-white",children:["Current Flashcards (",x.length,")"]}),x.length>0&&e.jsx("div",{className:"flex items-center space-x-3",children:e.jsxs("label",{className:"flex items-center space-x-2 text-sm text-gray-300",children:[e.jsx("input",{type:"checkbox",checked:J,onChange:r=>h(r.target.checked),className:"rounded border-gray-600 bg-gray-700 text-primary-500 focus:ring-primary-500 focus:ring-offset-gray-800"}),e.jsx("span",{children:"Select All"})]})})]}),e.jsx(Fe,{selectedCount:N.length,totalCount:x.length,onDeleteSelected:W,onClearSelection:v,isLoading:f}),x.length>0&&e.jsx("div",{className:"flex justify-end mb-4",children:e.jsx("button",{onClick:()=>t(!Z),className:"flex items-center space-x-2 px-3 py-2 bg-background-tertiary border border-gray-600 rounded-lg text-gray-300 hover:text-white hover:border-primary-500 transition-colors",children:Z?e.jsxs(e.Fragment,{children:[e.jsx(je,{className:"w-4 h-4"}),e.jsx("span",{children:"Hide Back Content"})]}):e.jsxs(e.Fragment,{children:[e.jsx(we,{className:"w-4 h-4"}),e.jsx("span",{children:"Show Back Content"})]})})}),x.length===0?e.jsx("div",{className:"text-center py-8 text-gray-400",children:"No flashcards yet. Add some manually or generate them with AI."}):e.jsx("div",{className:"space-y-2",children:x.map(r=>e.jsx("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600 hover:border-gray-500 transition-colors",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"flex-shrink-0 pt-1",children:e.jsx("input",{type:"checkbox",checked:N.includes(r.id),onChange:p=>n(r.id,p.target.checked),className:"rounded border-gray-600 bg-gray-700 text-primary-500 focus:ring-primary-500 focus:ring-offset-gray-800"})}),e.jsx("div",{className:"flex-1 min-w-0",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Front"}),e.jsx("p",{className:"text-white font-medium",children:r.front})]}),Z&&e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Back"}),e.jsx("p",{className:"text-gray-300",children:r.back})]}),e.jsxs("div",{className:"flex items-center space-x-4 text-xs text-gray-400",children:[r.is_ai_generated&&e.jsx("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded",children:"AI Generated"}),r.difficulty_level&&e.jsxs("span",{children:["Difficulty: ",typeof r.difficulty_level=="string"?te(r.difficulty_level):te(ce(r.difficulty_level))]}),e.jsxs("span",{children:["Reviewed: ",r.times_reviewed||0," times"]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2 ml-4",children:[e.jsx("button",{onClick:()=>ge(r),className:"text-gray-400 hover:text-white p-1",title:"Edit flashcard",children:"✏️"}),e.jsx("button",{onClick:()=>fe(r),className:"text-gray-400 hover:text-red-400 p-1",title:"Delete flashcard",children:"🗑️"})]})]})})]})},r.id))})]})]})},qe=({studySetId:s,questions:x,onQuestionAdded:i,onQuestionUpdated:g,onQuestionDeleted:o,onQuestionsGenerated:y})=>{const{alert:a,confirm:F}=se(),{user:j}=ee(),[E,T]=u.useState(!1),[l,_]=u.useState([]),[b,z]=u.useState(10),[L,I]=u.useState(""),[D,O]=u.useState(le.MEDIUM),[C,P]=u.useState(M.MEDIUM),[X,V]=u.useState(!1),[K,H]=u.useState(!1),[m,A]=u.useState({question_text:"",question_type:"multiple_choice",options:["","","",""],correct_answers:[],explanation:"",difficulty_level:3}),[R,$]=u.useState(null),[c,w]=u.useState({question_text:"",question_type:"multiple_choice",options:["","","",""],correct_answers:[],explanation:"",difficulty_level:3}),Y=async()=>{if(!m.question_text.trim()){await a({title:"Validation Error",message:"Question text is required.",variant:"error"});return}if(m.correct_answers.length===0){await a({title:"Validation Error",message:"At least one correct answer is required.",variant:"error"});return}if((m.question_type==="multiple_choice"||m.question_type==="select_all")&&m.options.filter(n=>n.trim().length>0).length<2){await a({title:"Validation Error",message:"Multiple choice and select all questions require at least 2 options.",variant:"error"});return}try{const t=await fetch(`/api/quiz-questions/study-set/${s}`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({question_text:m.question_text.trim(),question_type:m.question_type,options:m.question_type==="multiple_choice"||m.question_type==="select_all"?m.options.filter(h=>h.trim().length>0):null,correct_answers:m.correct_answers,explanation:m.explanation.trim()||null,difficulty_level:m.difficulty_level})});if(!t.ok)throw new Error("Failed to create question");const n=await t.json();i(n.data),A({question_text:"",question_type:"multiple_choice",options:["","","",""],correct_answers:[],explanation:"",difficulty_level:3}),H(!1),await a({title:"Success",message:"Question added successfully!",variant:"success"})}catch(t){await a({title:"Error",message:t.message||"Failed to add question",variant:"error"})}},G=async t=>{if(await F({title:"Delete Question",message:`Are you sure you want to delete this question?

${t.question_text.substring(0,100)}${t.question_text.length>100?"...":""}`,variant:"danger",confirmText:"Delete",cancelText:"Cancel"}))try{if(!(await fetch(`/api/quiz-questions/${t.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")}`}})).ok)throw new Error("Failed to delete question");o(t.id),await a({title:"Success",message:"Question deleted successfully!",variant:"success"})}catch(h){await a({title:"Error",message:h.message||"Failed to delete question",variant:"error"})}},B=t=>{$(t),w({question_text:t.question_text,question_type:t.question_type,options:t.options||["","","",""],correct_answers:t.correct_answers,explanation:t.explanation||"",difficulty_level:typeof t.difficulty_level=="string"?de(t.difficulty_level):t.difficulty_level||3})},N=async()=>{if(R){if(!c.question_text.trim()){await a({title:"Validation Error",message:"Question text is required.",variant:"error"});return}if(c.correct_answers.length===0){await a({title:"Validation Error",message:"At least one correct answer is required.",variant:"error"});return}if((c.question_type==="multiple_choice"||c.question_type==="select_all")&&c.options.filter(n=>n.trim().length>0).length<2){await a({title:"Validation Error",message:"Multiple choice and select all questions require at least 2 options.",variant:"error"});return}try{const t=await fetch(`/api/quiz-questions/${R.id}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({question_text:c.question_text.trim(),question_type:c.question_type,options:c.question_type==="multiple_choice"||c.question_type==="select_all"?c.options.filter(h=>h.trim().length>0):null,correct_answers:c.correct_answers,explanation:c.explanation.trim()||null,difficulty_level:c.difficulty_level})});if(!t.ok)throw new Error("Failed to update question");const n=await t.json();g(n.data),$(null),await a({title:"Success",message:"Question updated successfully!",variant:"success"})}catch(t){await a({title:"Error",message:t.message||"Failed to update question",variant:"error"})}}},Q=()=>{$(null),w({question_text:"",question_type:"multiple_choice",options:["","","",""],correct_answers:[],explanation:"",difficulty_level:3})},J=()=>Math.ceil(b/5),d=async()=>{if(l.length===0){await a({title:"No Documents Selected",message:"Please select at least one document to generate questions from.",variant:"warning"});return}const t=J();if(j&&j.credits_remaining<t){await a({title:"Insufficient Credits",message:`You need ${t} credits to generate ${b} questions, but you only have ${j.credits_remaining} credits remaining.`,variant:"warning"});return}if(await F({title:"Generate Questions",message:`Generate ${b} questions for ${t} credits?`,confirmText:"Generate",cancelText:"Cancel"})){V(!0);try{const h=await fetch("/api/ai/generate-more-quiz-questions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({studySetId:s,documentIds:l,count:b,customPrompt:L.trim()||void 0,difficultyLevel:D,contentLength:C})});if(!h.ok)throw new Error("Failed to generate questions");const v=await h.json();y(v.data.questions),j&&ee.getState().updateUser({credits_remaining:v.data.creditsRemaining}),await a({title:"Success",message:`Generated ${v.data.questions.length} questions successfully!`,variant:"success"}),_([]),I(""),T(!1)}catch(h){await a({title:"Error",message:h.message||"Failed to generate questions",variant:"error"})}finally{V(!1)}}},f=t=>{A(n=>({...n,question_type:t,options:t==="multiple_choice"||t==="select_all"?["","","",""]:[],correct_answers:[]}))},q=(t,n)=>{A(h=>({...h,options:h.options.map((v,W)=>W===t?n:v)}))},Z=t=>{A(n=>{const h=n.correct_answers.includes(t);return n.question_type==="multiple_choice"?{...n,correct_answers:h?[]:[t]}:{...n,correct_answers:h?n.correct_answers.filter(v=>v!==t):[...n.correct_answers,t]}})};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Question Management"}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsx(S,{onClick:()=>H(!K),variant:"secondary",size:"sm",children:K?"Cancel":"Add Question"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-sm text-gray-300",children:"AI Mode"}),e.jsx("button",{onClick:()=>T(!E),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${E?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${E?"translate-x-6":"translate-x-1"}`})})]})]})]}),K&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"Add New Question"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Text"}),e.jsx("textarea",{value:m.question_text,onChange:t=>A(n=>({...n,question_text:t.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter your question..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Type"}),e.jsxs("select",{value:m.question_type,onChange:t=>f(t.target.value),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:"multiple_choice",children:"Multiple Choice"}),e.jsx("option",{value:"select_all",children:"Select All That Apply"}),e.jsx("option",{value:"true_false",children:"True/False"}),e.jsx("option",{value:"short_answer",children:"Short Answer"})]})]}),(m.question_type==="multiple_choice"||m.question_type==="select_all")&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Answer Options"}),e.jsx("div",{className:"space-y-2",children:m.options.map((t,n)=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{type:"button",onClick:()=>Z(t),className:`flex-shrink-0 w-5 h-5 border-2 ${m.question_type==="multiple_choice"?"rounded-full":"rounded"} ${m.correct_answers.includes(t)?"bg-primary-500 border-primary-500":"border-gray-400"} flex items-center justify-center`,disabled:!t.trim(),children:m.correct_answers.includes(t)&&e.jsx("span",{className:"text-white text-xs",children:"✓"})}),e.jsx("input",{type:"text",value:t,onChange:h=>q(n,h.target.value),className:"flex-1 px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",placeholder:`Option ${n+1}`})]},n))}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:m.question_type==="multiple_choice"?"Click the circle to mark the correct answer":"Click the squares to mark all correct answers"})]}),m.question_type==="true_false"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Correct Answer"}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("button",{type:"button",onClick:()=>A(t=>({...t,correct_answers:["True"]})),className:`px-4 py-2 rounded-lg border ${m.correct_answers.includes("True")?"bg-primary-500 border-primary-500 text-white":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:"True"}),e.jsx("button",{type:"button",onClick:()=>A(t=>({...t,correct_answers:["False"]})),className:`px-4 py-2 rounded-lg border ${m.correct_answers.includes("False")?"bg-primary-500 border-primary-500 text-white":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:"False"})]})]}),m.question_type==="short_answer"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Acceptable Answers (one per line)"}),e.jsx("textarea",{value:m.correct_answers.join(`
`),onChange:t=>A(n=>({...n,correct_answers:t.target.value.split(`
`).filter(h=>h.trim())})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter acceptable answers, one per line..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Explanation (Optional)"}),e.jsx("textarea",{value:m.explanation,onChange:t=>A(n=>({...n,explanation:t.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:2,placeholder:"Explain the correct answer..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Difficulty Level"}),e.jsxs("select",{value:m.difficulty_level,onChange:t=>A(n=>({...n,difficulty_level:parseInt(t.target.value)})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:1,children:"1 - Very Easy"}),e.jsx("option",{value:2,children:"2 - Easy"}),e.jsx("option",{value:3,children:"3 - Medium"}),e.jsx("option",{value:4,children:"4 - Hard"}),e.jsx("option",{value:5,children:"5 - Very Hard"})]})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(S,{onClick:Y,variant:"primary",children:"Add Question"}),e.jsx(S,{onClick:()=>H(!1),variant:"secondary",children:"Cancel"})]})]})]}),R&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"Edit Question"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Text"}),e.jsx("textarea",{value:c.question_text,onChange:t=>w(n=>({...n,question_text:t.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter your question..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Type"}),e.jsxs("select",{value:c.question_type,onChange:t=>w(n=>({...n,question_type:t.target.value,options:t.target.value==="multiple_choice"||t.target.value==="select_all"?["","","",""]:[],correct_answers:[]})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:"multiple_choice",children:"Multiple Choice"}),e.jsx("option",{value:"select_all",children:"Select All That Apply"}),e.jsx("option",{value:"true_false",children:"True/False"}),e.jsx("option",{value:"short_answer",children:"Short Answer"})]})]}),(c.question_type==="multiple_choice"||c.question_type==="select_all")&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Answer Options"}),e.jsx("div",{className:"space-y-2",children:c.options.map((t,n)=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{type:"button",onClick:()=>{const h=c.correct_answers.includes(t);c.question_type==="multiple_choice"?w(v=>({...v,correct_answers:h?[]:[t]})):w(v=>({...v,correct_answers:h?v.correct_answers.filter(W=>W!==t):[...v.correct_answers,t]}))},className:`flex-shrink-0 w-5 h-5 border-2 ${c.question_type==="multiple_choice"?"rounded-full":"rounded"} ${c.correct_answers.includes(t)?"bg-primary-500 border-primary-500":"border-gray-400"} flex items-center justify-center`,disabled:!t.trim(),children:c.correct_answers.includes(t)&&e.jsx("span",{className:"text-white text-xs",children:"✓"})}),e.jsx("input",{type:"text",value:t,onChange:h=>w(v=>({...v,options:v.options.map((W,re)=>re===n?h.target.value:W)})),className:"flex-1 px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",placeholder:`Option ${n+1}`})]},n))}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:c.question_type==="multiple_choice"?"Click the circle to mark the correct answer":"Click the squares to mark all correct answers"})]}),c.question_type==="true_false"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Correct Answer"}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("button",{type:"button",onClick:()=>w(t=>({...t,correct_answers:["True"]})),className:`px-4 py-2 rounded-lg border ${c.correct_answers.includes("True")?"bg-primary-500 border-primary-500 text-white":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:"True"}),e.jsx("button",{type:"button",onClick:()=>w(t=>({...t,correct_answers:["False"]})),className:`px-4 py-2 rounded-lg border ${c.correct_answers.includes("False")?"bg-primary-500 border-primary-500 text-white":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:"False"})]})]}),c.question_type==="short_answer"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Acceptable Answers (one per line)"}),e.jsx("textarea",{value:c.correct_answers.join(`
`),onChange:t=>w(n=>({...n,correct_answers:t.target.value.split(`
`).filter(h=>h.trim())})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter acceptable answers, one per line..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Explanation (Optional)"}),e.jsx("textarea",{value:c.explanation,onChange:t=>w(n=>({...n,explanation:t.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:2,placeholder:"Explain the correct answer..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Difficulty Level"}),e.jsxs("select",{value:c.difficulty_level,onChange:t=>w(n=>({...n,difficulty_level:parseInt(t.target.value)})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:1,children:"1 - Very Easy"}),e.jsx("option",{value:2,children:"2 - Easy"}),e.jsx("option",{value:3,children:"3 - Medium"}),e.jsx("option",{value:4,children:"4 - Hard"}),e.jsx("option",{value:5,children:"5 - Very Hard"})]})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(S,{onClick:N,variant:"primary",children:"Save Changes"}),e.jsx(S,{onClick:Q,variant:"secondary",children:"Cancel"})]})]})]}),E&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"AI Question Generation"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Select Documents"}),e.jsx(me,{selectedDocuments:l,onSelectionChange:_,maxSelection:5})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Number of Questions"}),e.jsx("input",{type:"number",min:"1",max:"50",value:b,onChange:t=>z(parseInt(t.target.value)||1),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Credit Cost"}),e.jsxs("div",{className:"px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-primary-400 font-medium",children:[J()," credits"]})]})]}),e.jsx(oe,{value:D,onChange:O}),e.jsx(he,{value:C,onChange:P}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Custom Instructions (Optional)"}),e.jsx("textarea",{value:L,onChange:t=>I(t.target.value),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Add specific instructions for question generation..."})]}),e.jsx(S,{onClick:d,disabled:l.length===0||X,className:"w-full",variant:"primary",children:X?"Generating...":`Generate ${b} Questions`})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("h4",{className:"text-md font-medium text-white",children:["Current Questions (",x.length,")"]}),x.length===0?e.jsx("div",{className:"text-center py-8 text-gray-400",children:"No questions yet. Add some manually or generate them with AI."}):e.jsx("div",{className:"space-y-2",children:x.map(t=>e.jsx("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600 hover:border-gray-500 transition-colors",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Question"}),e.jsx("p",{className:"text-white font-medium",children:t.question_text})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Type"}),e.jsx("p",{className:"text-gray-300 capitalize",children:t.question_type.replace("_"," ")})]}),t.options&&e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Options"}),e.jsx("ul",{className:"text-gray-300 text-sm",children:t.options.map((n,h)=>e.jsxs("li",{className:`${t.correct_answers.includes(n)?"text-green-400 font-medium":""}`,children:[h+1,". ",n]},`${t.id}-option-${h}`))})]}),t.explanation&&e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Explanation"}),e.jsx("p",{className:"text-gray-300 text-sm",children:t.explanation})]}),e.jsxs("div",{className:"flex items-center space-x-4 text-xs text-gray-400",children:[t.is_ai_generated&&e.jsx("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded",children:"AI Generated"}),t.difficulty_level&&e.jsxs("span",{children:["Difficulty: ",typeof t.difficulty_level=="string"?te(t.difficulty_level):te(ce(t.difficulty_level))]}),e.jsxs("span",{children:["Attempted: ",t.times_attempted||0," times"]}),e.jsxs("span",{children:["Correct: ",t.times_correct||0," times"]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2 ml-4",children:[e.jsx("button",{onClick:()=>B(t),className:"text-gray-400 hover:text-primary-400 p-1",title:"Edit question",children:"✏️"}),e.jsx("button",{onClick:()=>G(t),className:"text-gray-400 hover:text-red-400 p-1",title:"Delete question",children:"🗑️"})]})]})},t.id))})]})]})},Te=({enabled:s,onChange:x,disabled:i=!1,className:g="",label:o="Shuffle Cards",description:y="Randomize the order of flashcards during study sessions"})=>{const a=()=>{i||x(!s)},F=j=>{(j.key===" "||j.key==="Enter")&&(j.preventDefault(),a())};return e.jsxs("div",{className:`flex items-center justify-between ${g}`,children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("span",{className:"text-sm font-medium text-white",children:o}),y&&e.jsx("span",{className:"text-xs text-gray-400",children:y})]})}),e.jsx("button",{type:"button",role:"switch","aria-checked":s,"aria-label":`${s?"Disable":"Enable"} ${o.toLowerCase()}`,onClick:a,onKeyDown:F,disabled:i,className:`
          relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ease-in-out
          focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-800
          ${i?"opacity-50 cursor-not-allowed bg-gray-600":s?"bg-primary-500 hover:bg-primary-600":"bg-gray-600 hover:bg-gray-500"}
        `,children:e.jsx("span",{className:`
            inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ease-in-out
            ${s?"translate-x-6":"translate-x-1"}
          `})})]})},ze=()=>{const{id:s}=Ne(),x=_e(),{studySetContent:i,isLoading:g,error:o,fetchStudySetContent:y}=ie(),{alert:a,confirm:F,prompt:j}=se(),{settings:E,updateSettings:T}=ue(),[l,_]=u.useState(null),[b,z]=u.useState("study"),[L,I]=u.useState("flashcards"),[D,O]=u.useState([]),[C,P]=u.useState([]),[X,V]=u.useState("");u.useEffect(()=>{s&&y(s).catch(console.error)},[s,y]),u.useEffect(()=>{i!=null&&i.studySet&&(V(i.studySet.name),O(i.flashcards||[]),P(i.questions||[]))},[i]);const K=async()=>{if(!(!s||!l))try{const d=(E==null?void 0:E.shuffle_flashcards)||!1;await ie.getState().startStudySession(s,l,d),x(`/study/${s}/${l}`)}catch(d){await a({title:"Error",message:d.message||"Failed to start study session",variant:"error"})}},H=async()=>{if(!s||!(i!=null&&i.studySet))return;const d=await j({title:"Rename Study Set",message:"Enter a new name for this study set:",defaultValue:i.studySet.name});if(!(d===null||d.trim()===i.studySet.name)){if(!d.trim()){await a({title:"Invalid Name",message:"Study set name cannot be empty.",variant:"error"});return}try{if(!(await fetch(`/api/study-sets/${s}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({name:d.trim()})})).ok)throw new Error("Failed to rename study set");V(d.trim()),await a({title:"Success",message:"Study set renamed successfully!",variant:"success"}),await y(s)}catch(f){await a({title:"Error",message:f.message||"Failed to rename study set",variant:"error"})}}},m=async()=>{if(!(!s||!(i!=null&&i.studySet)||!await F({title:"Delete Study Set",message:`Are you sure you want to delete "${i.studySet.name}"?

This action cannot be undone and will delete all flashcards and quiz questions in this set.`,variant:"danger",confirmText:"Delete Study Set",cancelText:"Cancel"})))try{if(!(await fetch(`/api/study-sets/${s}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")}`}})).ok)throw new Error("Failed to delete study set");await a({title:"Success",message:"Study set deleted successfully!",variant:"success"}),x("/dashboard")}catch(f){await a({title:"Error",message:f.message||"Failed to delete study set",variant:"error"})}},A=d=>{O(f=>[...f,d])},R=d=>{O(f=>f.map(q=>q.id===d.id?d:q))},$=d=>{O(f=>f.filter(q=>q.id!==d))},c=d=>{O(f=>[...f,...d])},w=d=>{P(f=>[...f,d])},Y=d=>{P(f=>f.map(q=>q.id===d.id?d:q))},G=d=>{P(f=>f.filter(q=>q.id!==d))},B=d=>{P(f=>[...f,...d])};if(g)return e.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsxs("div",{className:"flex items-center justify-center py-12",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"}),e.jsx("span",{className:"ml-3 text-gray-400",children:"Loading study set..."})]})});if(o||!(i!=null&&i.studySet))return e.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-red-400 mb-4",children:o||"Study set not found"}),e.jsx(S,{onClick:()=>x("/dashboard"),variant:"secondary",children:"Back to Study Sets"})]})});const{studySet:N}=i,Q=D&&D.length>0,J=C&&C.length>0;return e.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("button",{onClick:()=>x("/dashboard"),className:"text-gray-400 hover:text-white mb-4 flex items-center",children:"← Back to Study Sets"}),e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("h1",{className:"text-3xl font-bold text-white",children:X}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(S,{onClick:H,variant:"secondary",size:"sm",children:"✏️ Rename"}),e.jsx(S,{onClick:m,variant:"danger",size:"sm",children:"🗑️ Delete"})]})]}),e.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-400",children:[e.jsx("span",{className:"capitalize",children:N.type}),N.is_ai_generated&&e.jsx("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded",children:"AI Generated"}),e.jsxs("span",{children:["Created ",new Date(N.created_at).toLocaleDateString()]})]})]}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"border-b border-gray-600",children:e.jsxs("nav",{className:"-mb-px flex space-x-8",children:[e.jsx("button",{onClick:()=>z("study"),className:`
                py-2 px-1 border-b-2 font-medium text-sm transition-colors
                ${b==="study"?"border-primary-500 text-primary-400":"border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300"}
              `,children:"📚 Study Mode"}),e.jsx("button",{onClick:()=>z("manage"),className:`
                py-2 px-1 border-b-2 font-medium text-sm transition-colors
                ${b==="manage"?"border-primary-500 text-primary-400":"border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300"}
              `,children:"⚙️ Manage Content"})]})})}),b==="study"&&e.jsx(e.Fragment,{children:e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 mb-6",children:[e.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"Choose Study Mode"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[Q&&e.jsx("div",{className:`
                    p-4 rounded-lg border-2 cursor-pointer transition-all
                    ${l==="flashcards"?"border-primary-500 bg-primary-500/10":"border-gray-600 hover:border-gray-500"}
                  `,onClick:()=>_("flashcards"),children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"text-2xl",children:"🃏"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-white",children:"Flashcard Review"}),e.jsxs("p",{className:"text-sm text-gray-400",children:[D==null?void 0:D.length," flashcards • Interactive review"]})]})]})}),J&&e.jsx("div",{className:`
                    p-4 rounded-lg border-2 cursor-pointer transition-all
                    ${l==="quiz"?"border-primary-500 bg-primary-500/10":"border-gray-600 hover:border-gray-500"}
                  `,onClick:()=>_("quiz"),children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"text-2xl",children:"📝"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-white",children:"Quiz Practice"}),e.jsxs("p",{className:"text-sm text-gray-400",children:[C==null?void 0:C.length," questions • Test your knowledge"]})]})]})})]}),l&&E&&e.jsx("div",{className:"mb-6 p-4 bg-background-tertiary rounded-lg border border-gray-600",children:e.jsx(Te,{enabled:E.shuffle_flashcards,onChange:async d=>{try{await T({shuffle_flashcards:d})}catch(f){console.error("Failed to update shuffle setting:",f)}},label:"Shuffle Cards",description:"Randomize the order of flashcards during study sessions"})}),e.jsx(S,{onClick:K,disabled:!l,className:"w-full",size:"lg",children:l?`Start ${l==="flashcards"?"Flashcard Review":"Quiz Practice"}`:"Select a study mode"})]})}),b==="manage"&&s&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 mb-6",children:[e.jsxs("div",{className:"flex space-x-1 mb-6",children:[e.jsxs("button",{onClick:()=>I("flashcards"),className:`
                py-2 px-4 rounded-lg font-medium text-sm transition-colors
                ${L==="flashcards"?"bg-primary-500 text-white":"bg-background-primary text-gray-400 hover:text-gray-300"}
              `,children:["📚 Flashcards (",D.length,")"]}),e.jsxs("button",{onClick:()=>I("quiz"),className:`
                py-2 px-4 rounded-lg font-medium text-sm transition-colors
                ${L==="quiz"?"bg-primary-500 text-white":"bg-background-primary text-gray-400 hover:text-gray-300"}
              `,children:["❓ Quiz Questions (",C.length,")"]})]}),L==="flashcards"&&e.jsx($e,{studySetId:s,flashcards:D,onFlashcardAdded:A,onFlashcardUpdated:R,onFlashcardDeleted:$,onFlashcardsGenerated:c}),L==="quiz"&&e.jsx(qe,{studySetId:s,questions:C,onQuestionAdded:w,onQuestionUpdated:Y,onQuestionDeleted:G,onQuestionsGenerated:B})]}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6",children:[e.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:"Study Set Details"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Content"}),e.jsxs("div",{className:"space-y-1 text-sm text-gray-400",children:[Q&&e.jsxs("div",{children:[D.length," flashcards"]}),J&&e.jsxs("div",{children:[C==null?void 0:C.length," quiz questions"]}),!Q&&!J&&e.jsx("div",{className:"text-gray-500",children:"No content yet"})]})]}),N.source_documents&&N.source_documents.length>0&&e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Source Documents"}),e.jsx("div",{className:"space-y-1 text-sm text-gray-400",children:N.source_documents.map((d,f)=>e.jsx("div",{children:d.filename},f))})]}),N.custom_prompt&&e.jsxs("div",{className:"md:col-span-2",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Custom Instructions"}),e.jsx("p",{className:"text-sm text-gray-400",children:N.custom_prompt})]})]})]})]})};export{ze as StudySetPage};
