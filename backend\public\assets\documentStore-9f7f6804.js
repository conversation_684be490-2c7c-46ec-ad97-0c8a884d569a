import{c}from"./index-8171bd56.js";const l=c(n=>({documents:[],selectedDocuments:new Set,isLoading:!1,uploadProgress:{},fetchDocuments:async()=>{n({isLoading:!0});try{const t=localStorage.getItem("auth_token"),e=await fetch("/api/documents",{headers:{Authorization:`Bearer ${t}`}});if(!e.ok)throw new Error("Failed to fetch documents");const o=await e.json();if(o.success)n({documents:o.data,isLoading:!1});else throw new Error(o.error)}catch(t){throw console.error("Fetch documents error:",t),n({isLoading:!1}),t}},uploadDocument:async t=>{const e=new FormData;e.append("document",t);try{const o=localStorage.getItem("auth_token"),r=await fetch("/api/documents/upload",{method:"POST",headers:{Authorization:`Bear<PERSON> ${o}`},body:e});if(!r.ok){const a=await r.json();throw new Error(a.error||"Upload failed")}const s=await r.json();if(s.success)return n(a=>({documents:[s.data,...a.documents],uploadProgress:{...a.uploadProgress,[t.name]:100}})),s.data;throw new Error(s.error)}catch(o){throw console.error("Upload document error:",o),o}},deleteDocument:async t=>{try{const e=localStorage.getItem("auth_token"),o=await fetch(`/api/documents/${t}`,{method:"DELETE",headers:{Authorization:`Bearer ${e}`}});if(!o.ok){const r=await o.json();throw new Error(r.error||"Delete failed")}n(r=>({documents:r.documents.filter(s=>s.id!==t),selectedDocuments:new Set([...r.selectedDocuments].filter(s=>s!==t))}))}catch(e){throw console.error("Delete document error:",e),e}},searchDocuments:async t=>{try{const e=localStorage.getItem("auth_token"),o=await fetch(`/api/documents/search?q=${encodeURIComponent(t)}`,{headers:{Authorization:`Bearer ${e}`}});if(!o.ok)throw new Error("Search failed");const r=await o.json();return r.success?r.data:[]}catch(e){return console.error("Search documents error:",e),[]}},getDocument:async t=>{try{const e=localStorage.getItem("auth_token"),o=await fetch(`/api/documents/${t}`,{headers:{Authorization:`Bearer ${e}`}});if(!o.ok)return null;const r=await o.json();return r.success?r.data:null}catch(e){return console.error("Get document error:",e),null}},toggleDocumentSelection:t=>{n(e=>{const o=new Set(e.selectedDocuments);return o.has(t)?o.delete(t):o.add(t),{selectedDocuments:o}})},clearSelection:()=>{n({selectedDocuments:new Set})},selectAll:()=>{n(t=>({selectedDocuments:new Set(t.documents.map(e=>e.id))}))},setUploadProgress:(t,e)=>{n(o=>({uploadProgress:{...o.uploadProgress,[t]:e}}))}}));export{l as u};
